"""
Word内容编辑工具 - 提供段落、标题、表格、图片等内容编辑功能
"""
import os
import json
from typing import List, Optional
from docx import Document
from docx.shared import Inches, Pt

from .utils import (
    check_file_writeable,
    ensure_docx_extension,
    find_and_replace_text,
    ensure_heading_style,
    ensure_table_style,
    insert_header_near_text,
    insert_line_or_paragraph_near_text,
    insert_numbered_list_near_text,
    get_paragraph_text,
    find_text
)


async def add_paragraph(filename: str, text: str, style: Optional[str] = None) -> str:
    """
    向Word文档添加段落
    
    Args:
        filename: Word文档路径
        text: 段落文本
        style: 可选的段落样式名称
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本或创建新文档。"
    
    try:
        doc = Document(filename)
        paragraph = doc.add_paragraph(text)
        
        if style:
            try:
                paragraph.style = style
            except KeyError:
                # 样式不存在，使用普通样式并报告
                paragraph.style = doc.styles['Normal']
                doc.save(filename)
                return f"样式 '{style}' 未找到，段落已使用默认样式添加到 {filename}"
        
        doc.save(filename)
        return f"段落已添加到 {filename}"
    except Exception as e:
        return f"添加段落失败: {str(e)}"


async def add_heading(filename: str, text: str, level: int = 1) -> str:
    """
    向Word文档添加标题
    
    Args:
        filename: Word文档路径
        text: 标题文本
        level: 标题级别（1-9，其中1是最高级别）
    """
    filename = ensure_docx_extension(filename)
    
    # 确保level转换为整数
    try:
        level = int(level)
    except (ValueError, TypeError):
        return "无效参数: level必须是1到9之间的整数"
    
    # 验证级别范围
    if level < 1 or level > 9:
        return f"无效的标题级别: {level}。级别必须在1到9之间。"
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本或创建新文档。"
    
    try:
        doc = Document(filename)
        
        # 确保标题样式存在
        ensure_heading_style(doc)
        
        # 尝试使用样式添加标题
        try:
            heading = doc.add_heading(text, level=level)
            doc.save(filename)
            return f"标题 '{text}' (级别 {level}) 已添加到 {filename}"
        except Exception as style_error:
            # 如果基于样式的方法失败，使用直接格式化
            paragraph = doc.add_paragraph(text)
            paragraph.style = doc.styles['Normal']
            run = paragraph.runs[0]
            run.bold = True
            # 根据标题级别调整大小
            if level == 1:
                run.font.size = Pt(16)
            elif level == 2:
                run.font.size = Pt(14)
            else:
                run.font.size = Pt(12)
            
            doc.save(filename)
            return f"标题 '{text}' 已添加到 {filename}，使用直接格式化（样式不可用）"
    except Exception as e:
        return f"添加标题失败: {str(e)}"


async def add_table(filename: str, rows: int, cols: int, data: Optional[List[List[str]]] = None) -> str:
    """
    向Word文档添加表格
    
    Args:
        filename: Word文档路径
        rows: 表格行数
        cols: 表格列数
        data: 可选的二维数组数据来填充表格
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本或创建新文档。"
    
    try:
        doc = Document(filename)
        table = doc.add_table(rows=rows, cols=cols)
        
        # 尝试设置表格样式
        try:
            table.style = 'Table Grid'
        except KeyError:
            # 如果样式不存在，添加基本边框
            pass
        
        # 如果提供了数据，填充表格
        if data:
            for i, row_data in enumerate(data):
                if i >= rows:
                    break
                for j, cell_text in enumerate(row_data):
                    if j >= cols:
                        break
                    table.cell(i, j).text = str(cell_text)
        
        doc.save(filename)
        return f"表格 ({rows}x{cols}) 已添加到 {filename}"
    except Exception as e:
        return f"添加表格失败: {str(e)}"


async def add_picture(filename: str, image_path: str, width: Optional[float] = None) -> str:
    """
    向Word文档添加图片
    
    Args:
        filename: Word文档路径
        image_path: 图片文件路径
        width: 可选的宽度（英寸）（按比例缩放）
    """
    filename = ensure_docx_extension(filename)
    
    # 验证文档存在
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 获取绝对路径以便更好地诊断
    abs_filename = os.path.abspath(filename)
    abs_image_path = os.path.abspath(image_path)
    
    # 验证图片存在，提供改进的错误消息
    if not os.path.exists(abs_image_path):
        return f"未找到图片文件: {abs_image_path}"
    
    # 检查图片文件大小
    try:
        image_size = os.path.getsize(abs_image_path) / 1024  # KB大小
        if image_size <= 0:
            return f"图片文件似乎为空: {abs_image_path} (0 KB)"
    except Exception as size_error:
        return f"检查图片文件时出错: {str(size_error)}"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(abs_filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本或创建新文档。"
    
    try:
        doc = Document(abs_filename)
        # 额外的诊断信息
        diagnostic = f"尝试添加图片 ({abs_image_path}, {image_size:.2f} KB) 到文档 ({abs_filename})"
        
        try:
            if width:
                doc.add_picture(abs_image_path, width=Inches(width))
            else:
                doc.add_picture(abs_image_path)
            doc.save(abs_filename)
            return f"图片 {image_path} 已添加到 {filename}"
        except Exception as inner_error:
            # 特定操作的更详细错误
            error_type = type(inner_error).__name__
            error_msg = str(inner_error)
            return f"添加图片失败: {error_type} - {error_msg or '无错误详情'}\n诊断信息: {diagnostic}"
    except Exception as outer_error:
        # 回退错误处理
        error_type = type(outer_error).__name__
        error_msg = str(outer_error)
        return f"文档处理错误: {error_type} - {error_msg or '无错误详情'}"


async def add_page_break(filename: str) -> str:
    """
    向文档添加分页符
    
    Args:
        filename: Word文档路径
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"
    
    try:
        doc = Document(filename)
        doc.add_page_break()
        doc.save(filename)
        return f"分页符已添加到 {filename}。"
    except Exception as e:
        return f"添加分页符失败: {str(e)}"


async def delete_paragraph(filename: str, paragraph_index: int) -> str:
    """
    从文档中删除段落

    Args:
        filename: Word文档路径
        paragraph_index: 要删除的段落索引（从0开始）
    """
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 验证段落索引
        if paragraph_index < 0 or paragraph_index >= len(doc.paragraphs):
            return f"无效的段落索引。文档有 {len(doc.paragraphs)} 个段落 (0-{len(doc.paragraphs)-1})。"

        # 删除段落（通过移除其内容并设置为空）
        # 注意：python-docx不支持真正的段落删除，这是一个变通方法
        paragraph = doc.paragraphs[paragraph_index]
        p = paragraph._p
        p.getparent().remove(p)

        doc.save(filename)
        return f"索引 {paragraph_index} 处的段落已成功删除。"
    except Exception as e:
        return f"删除段落失败: {str(e)}"


async def search_and_replace(filename: str, find_text: str, replace_text: str) -> str:
    """
    搜索文本并替换所有出现的地方

    Args:
        filename: Word文档路径
        find_text: 要搜索的文本
        replace_text: 要替换的文本
    """
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 执行查找和替换
        count = find_and_replace_text(doc, find_text, replace_text)

        if count > 0:
            doc.save(filename)
            return f"已将 '{find_text}' 替换为 '{replace_text}'，共 {count} 处。"
        else:
            return f"未找到 '{find_text}'。"
    except Exception as e:
        return f"搜索和替换失败: {str(e)}"


async def insert_header_near_text_tool(filename: str, target_text: str = None, header_title: str = None,
                                      position: str = 'after', header_style: str = 'Heading 1',
                                      target_paragraph_index: int = None) -> str:
    """在指定文本附近插入标题（使用指定样式）。通过文本或段落索引指定。"""
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    return insert_header_near_text(filename, target_text, header_title, position, header_style, target_paragraph_index)


async def insert_line_or_paragraph_near_text_tool(filename: str, target_text: str = None, line_text: str = None,
                                                 position: str = 'after', line_style: str = None,
                                                 target_paragraph_index: int = None) -> str:
    """
    在指定文本附近插入新行或段落（使用指定或匹配的样式）。通过文本或段落索引指定。
    """
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    return insert_line_or_paragraph_near_text(filename, target_text, line_text, position, line_style, target_paragraph_index)


async def insert_numbered_list_near_text_tool(filename: str, target_text: str = None, list_items: list = None,
                                             position: str = 'after', target_paragraph_index: int = None) -> str:
    """在指定文本附近插入编号列表。通过文本或段落索引指定。"""
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    return insert_numbered_list_near_text(filename, target_text, list_items, position, target_paragraph_index)


async def get_paragraph_text_from_document(filename: str, paragraph_index: int) -> str:
    """
    获取Word文档中特定段落的文本

    Args:
        filename: Word文档路径
        paragraph_index: 段落索引（从0开始）
    """
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    if paragraph_index < 0:
        return "无效参数: paragraph_index必须是非负整数"

    try:
        result = get_paragraph_text(filename, paragraph_index)
        return json.dumps(result, indent=2, ensure_ascii=False)
    except Exception as e:
        return f"获取段落文本失败: {str(e)}"


async def find_text_in_document(filename: str, text_to_find: str, match_case: bool = True, whole_word: bool = False) -> str:
    """
    在Word文档中查找指定文本的出现位置

    Args:
        filename: Word文档路径
        text_to_find: 要在文档中搜索的文本
        match_case: 是否区分大小写（True）或忽略大小写（False）
        whole_word: 是否只匹配完整单词（True）或子字符串（False）
    """
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    if not text_to_find:
        return "搜索文本不能为空"

    try:
        result = find_text(filename, text_to_find, match_case, whole_word)
        return json.dumps(result, indent=2, ensure_ascii=False)
    except Exception as e:
        return f"搜索文本失败: {str(e)}"
