"""
Word内容编辑 MCP服务主程序

提供Word文档的内容编辑功能，包括段落、标题、表格、图片等内容的添加和编辑。
"""
import os
import sys

# 设置FastMCP所需的环境变量
os.environ.setdefault('FASTMCP_LOG_LEVEL', 'INFO')

from fastmcp import FastMCP
from .tools import (
    add_paragraph,
    add_heading,
    add_table,
    add_page_break,
    delete_paragraph,
    search_and_replace,
    insert_line_or_paragraph_near_text_tool,
    get_paragraph_text_from_document,
    find_text_in_document
)





# 初始化FastMCP服务器
mcp = FastMCP("Word内容编辑服务")


def register_tools():
    """使用FastMCP装饰器注册所有工具"""
    
    @mcp.tool()
    def add_paragraph_tool(filename: str, text: str, style: str = None):
        """向Word文档添加段落"""
        return add_paragraph(filename, text, style)
    
    @mcp.tool()
    def add_heading_tool(filename: str, text: str, level: int = 1):
        """向Word文档添加标题"""
        return add_heading(filename, text, level)
    
    @mcp.tool()
    def add_table_tool(filename: str, rows: int, cols: int, data: list = None):
        """向Word文档添加表格"""
        return add_table(filename, rows, cols, data)
    
    @mcp.tool()
    def add_page_break_tool(filename: str):
        """向文档添加分页符"""
        return add_page_break(filename)

    @mcp.tool()
    def delete_paragraph_tool(filename: str, paragraph_index: int):
        """从文档中删除段落"""
        return delete_paragraph(filename, paragraph_index)

    @mcp.tool()
    def search_and_replace_tool(filename: str, find_text: str, replace_text: str):
        """搜索文本并替换所有出现的地方"""
        return search_and_replace(filename, find_text, replace_text)

    @mcp.tool()
    def insert_line_or_paragraph_near_text(filename: str, target_text: str = None, line_text: str = None,
                                          position: str = 'after', line_style: str = None,
                                          target_paragraph_index: int = None):
        """在指定文本附近插入新行或段落"""
        return insert_line_or_paragraph_near_text_tool(filename, target_text, line_text, position, line_style, target_paragraph_index)

    @mcp.tool()
    async def get_paragraph_text_from_document_tool(filename: str, paragraph_index: int):
        """获取Word文档中特定段落的文本"""
        return await get_paragraph_text_from_document(filename, paragraph_index)

    @mcp.tool()
    async def find_text_in_document_tool(filename: str, text_to_find: str, match_case: bool = True, whole_word: bool = False):
        """在Word文档中查找指定文本的出现位置"""
        return await find_text_in_document(filename, text_to_find, match_case, whole_word)


def main():
    """服务器的主入口点 - 只支持stdio传输"""
    # 注册所有工具
    register_tools()

    print("启动Word内容编辑MCP服务器...")

    try:
        # 只使用stdio传输运行
        mcp.run(transport='stdio')
    except KeyboardInterrupt:
        print("\n正在关闭Word内容编辑服务器...")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
