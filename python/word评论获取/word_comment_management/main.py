"""
Word评论获取 MCP服务主程序

提供Word文档的评论获取功能（简化版 - 只支持stdio传输）。
"""
import os
import sys

# 设置FastMCP所需的环境变量
os.environ.setdefault('FASTMCP_LOG_LEVEL', 'INFO')

from fastmcp import FastMCP
from .tools import (
    get_all_comments,
    get_comments_by_author
)

# 初始化FastMCP服务器
mcp = FastMCP("Word评论获取服务")

def register_tools():
    """使用FastMCP装饰器注册所有工具"""

    @mcp.tool()
    def get_all_comments_tool(filename: str):
        """提取文档中的所有评论"""
        return get_all_comments(filename)

    @mcp.tool()
    def get_comments_by_author_tool(filename: str, author_name: str):
        """按作者获取评论"""
        return get_comments_by_author(filename, author_name)

def main():
    """服务器的主入口点 - 只支持stdio传输"""
    # 注册所有工具
    register_tools()

    print("启动Word评论获取MCP服务器...")

    try:
        # 只使用stdio传输运行
        mcp.run(transport='stdio')
    except KeyboardInterrupt:
        print("\n正在关闭Word评论获取服务器...")
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
