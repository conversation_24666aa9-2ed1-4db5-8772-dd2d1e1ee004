"""
Word评论获取工具 - 提供评论获取功能
"""
import os
from typing import List, Optional
from docx import Document

from .utils import check_file_writeable, ensure_docx_extension


async def get_all_comments(filename: str) -> str:
    """提取文档中的所有评论"""
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    try:
        doc = Document(filename)
        
        # 注意：python-docx对评论的支持有限
        # 这里提供基本的占位符实现
        return "评论提取功能需要更复杂的XML解析实现。python-docx对评论的支持有限。"
    except Exception as e:
        return f"获取评论失败: {str(e)}"


async def get_comments_by_author(filename: str, author_name: str) -> str:
    """按作者获取评论"""
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    try:
        # 占位符实现
        return f"按作者 '{author_name}' 获取评论的功能需要更复杂的实现"
    except Exception as e:
        return f"按作者获取评论失败: {str(e)}"
