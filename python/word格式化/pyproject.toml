[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "word-formatting-mcp"
version = "1.0.0"
description = "Word格式化MCP服务 - 提供样式、文本格式化、表格格式化等功能"
authors = [
    {name = "Word MCP Services", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
dependencies = [
    "python-docx>=1.1.0",
    "fastmcp>=2.8.1",
    "lxml>=4.9.0",
]

[project.scripts]
word-formatting = "word_formatting.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["word_formatting*"]
